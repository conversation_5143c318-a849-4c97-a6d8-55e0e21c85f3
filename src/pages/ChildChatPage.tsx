import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import ChatFilterPills from '../components/ChatFilterPills';
import ChildChatItem from '../components/ChildChatItem';
import ChildChatConversation from '../components/ChildChatConversation';
import ChildMobileNavbar from '../components/ChildMobileNavbar';
import CallInterface from '../components/CallInterface';
import { Chat, CallRecord } from '../types/schema';
import { ChatFilterType, ChatType, CallType, CallStatus, MessageType, MessageStatus, ChildDashboardSection, OnlineStatus, ChatStatus } from '../types/enums';
import { mockFamilyMembers, mockFamilyChat } from '../data/ChatMockData';

// Mock data específico para crianças
const mockChildUser = {
  id: 'child-123',
  name: '<PERSON>',
  avatar: '👦',
  onlineStatus: OnlineStatus.ONLINE,
  lastSeen: new Date()
};

const mockChildChats: Chat[] = [
  {
    ...mockFamilyChat,
    id: 'family-chat-child',
    name: '👨‍👩‍👧‍👦 Família',
    type: ChatType.FAMILY,
    lastMessage: {
      id: 'msg-1',
      senderId: 'parent-1',
      senderName: 'Mãe',
      content: 'João, não te esqueças de arrumar o quarto! 😊',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 2,
    status: ChatStatus.ACTIVE,
    isPinned: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-1',
    name: '💬 Mãe',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(0, 2),
    lastMessage: {
      id: 'msg-2',
      senderId: mockChildUser.id,
      senderName: mockChildUser.name,
      content: 'Já arrumei o quarto! 🎉',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 min ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: 'chat-parent-2',
    name: '💬 Pai',
    type: ChatType.PRIVATE,
    participants: mockFamilyMembers.slice(1, 3),
    lastMessage: {
      id: 'msg-3',
      senderId: 'parent-2',
      senderName: 'Pai',
      content: 'Bom trabalho no teste de matemática! ⭐',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      status: MessageStatus.SENT,
    },
    unreadCount: 0,
    status: ChatStatus.ACTIVE,
    isPinned: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
];

/**
 * ChildChatPage component - Chat interface for children
 * Uses same visual components as adult chat but with child-specific functionality
 */
const ChildChatPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState<ChatFilterType>(ChatFilterType.ALL);
  const [chats, setChats] = useState<Chat[]>(mockChildChats);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [activeCall, setActiveCall] = useState<CallRecord | null>(null);
  const [isCallActive, setIsCallActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeSection, setActiveSection] = useState<ChildDashboardSection>(ChildDashboardSection.FAMILY_CHAT);

  const currentUser = mockChildUser;

  // Filter chats based on active filter
  const getFilteredChats = useCallback((): Chat[] => {
    let filteredChats = [...chats];

    switch (activeFilter) {
      case ChatFilterType.UNREAD:
        filteredChats = filteredChats.filter(chat => chat.unreadCount > 0);
        break;
      case ChatFilterType.RECEIVED:
        filteredChats = filteredChats.filter(chat => 
          chat.lastMessage && chat.lastMessage.senderId !== currentUser.id
        );
        break;
      case ChatFilterType.ARCHIVED:
        filteredChats = filteredChats.filter(chat => chat.status === 'archived');
        break;
      case ChatFilterType.ALL:
      default:
        filteredChats = filteredChats.filter(chat => chat.status !== 'archived');
        break;
    }

    return filteredChats.sort((a, b) => {
      // Pinned chats first
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      // Then by last message time
      const aTime = a.lastMessage?.timestamp || new Date(0);
      const bTime = b.lastMessage?.timestamp || new Date(0);
      return bTime.getTime() - aTime.getTime();
    });
  }, [chats, activeFilter]);

  const handleChatSelect = (chatId: string) => {
    setSelectedChat(chatId);
    
    // Mark messages as read
    setChats(prev => prev.map(chat => 
      chat.id === chatId 
        ? { ...chat, unreadCount: 0 }
        : chat
    ));
  };

  const handleBackToList = () => {
    setSelectedChat(null);
  };

  const handleSwipeAction = (chatId: string, action: string) => {
    setChats(prev => prev.filter(chat => 
      chat.id !== chatId || action !== 'delete'
    ).map(chat => 
      chat.id === chatId && action === 'archive'
        ? { ...chat, status: 'archived' as const }
        : chat
    ).filter(Boolean) as Chat[]
    );
  };

  const handleStartCall = (type: CallType) => {
    const currentChat = chats.find(chat => chat.id === selectedChat);
    const participants = currentChat ? currentChat.participants : mockFamilyMembers;

    const newCall: CallRecord = {
      id: `call-${Date.now()}`,
      type,
      status: CallStatus.OUTGOING,
      participants,
      duration: 0,
      timestamp: new Date(),
      initiatorId: currentUser.id
    };

    setActiveCall(newCall);
    setIsCallActive(true);

    // Simulate call connecting
    setTimeout(() => {
      setActiveCall(prev => prev ? { ...prev, status: CallStatus.ACTIVE } : null);
    }, 3000);

    console.log('Started call:', type);
  };

  const handleEndCall = () => {
    setActiveCall(null);
    setIsCallActive(false);
    console.log('Call ended');
  };

  const handleToggleVideo = () => {
    console.log('Toggle video');
  };

  const handleToggleMute = () => {
    console.log('Toggle mute');
  };

  const handleSectionChange = (section: ChildDashboardSection) => {
    setActiveSection(section);
    
    switch (section) {
      case ChildDashboardSection.HOME:
        navigate('/child-dashboard');
        break;
      case ChildDashboardSection.MY_TASKS:
        navigate('/child-tasks');
        break;
      case ChildDashboardSection.MY_REWARDS:
        navigate('/child-rewards');
        break;
      case ChildDashboardSection.FAMILY_CHAT:
        // Stay on chat page
        break;
      default:
        break;
    }
  };

  // Infinite scroll handler
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    
    if (scrollHeight - scrollTop === clientHeight && !loading) {
      setLoading(true);
      
      // Simulate loading more chats
      setTimeout(() => {
        setLoading(false);
        console.log('Load more chats...');
      }, 1000);
    }
  }, [loading]);

  const filteredChats = getFilteredChats();
  const unreadCount = chats.filter(chat => chat.unreadCount > 0).length;
  const archivedCount = chats.filter(chat => chat.status === 'archived').length;
  const currentChat = chats.find(chat => chat.id === selectedChat);

  // If a chat is selected, show conversation view
  if (selectedChat && currentChat) {
    return (
      <div className="child-chat-page">
        {/* Header */}
        <div className="child-chat-header text-center p-3">
          <div className="container">
            <div className="row align-items-center">
              <div className="col-auto">
                <button
                  className="btn btn-outline-secondary btn-sm mobile-touch-target"
                  onClick={handleBackToList}
                  title="Voltar"
                  style={{
                    background: 'linear-gradient(135deg, #fff 0%, #fff8f0 100%)',
                    border: '2px solid var(--st-orange)',
                    borderRadius: '15px',
                    minWidth: '44px',
                    minHeight: '44px'
                  }}
                >
                  <i className="fas fa-arrow-left" style={{ color: 'var(--st-orange)' }}></i>
                  <span className="d-none d-sm-inline ms-1" style={{ color: 'var(--st-orange)' }}>Voltar</span>
                </button>
              </div>
              <div className="col">
                <div className="child-chat-avatar mb-2">
                  {currentChat.type === ChatType.FAMILY ? '👨‍👩‍👧‍👦' : '💬'}
                </div>
                <h2 className="child-chat-title mb-1">
                  {currentChat.name}
                </h2>
                <p className="child-chat-subtitle mb-0">
                  {currentChat.type === ChatType.FAMILY ? '🏠 Chat da família' : '💬 Conversa privada'}
                </p>
              </div>
              <div className="col-auto">
                <div className="btn-group">
                  <button
                    className="btn btn-sm mobile-touch-target"
                    onClick={() => handleStartCall(CallType.AUDIO)}
                    title="Chamada de voz"
                    style={{
                      background: 'linear-gradient(135deg, var(--st-green) 0%, var(--st-blue) 100%)',
                      border: '2px solid var(--st-green)',
                      borderRadius: '15px',
                      color: 'white',
                      minWidth: '44px',
                      minHeight: '44px',
                      marginRight: '0.5rem'
                    }}
                  >
                    <i className="fas fa-phone"></i>
                  </button>
                  <button
                    className="btn btn-sm mobile-touch-target"
                    onClick={() => handleStartCall(CallType.VIDEO)}
                    title="Chamada de vídeo"
                    style={{
                      background: 'linear-gradient(135deg, var(--st-blue) 0%, var(--st-purple) 100%)',
                      border: '2px solid var(--st-blue)',
                      borderRadius: '15px',
                      color: 'white',
                      minWidth: '44px',
                      minHeight: '44px'
                    }}
                  >
                    <i className="fas fa-video"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conversation */}
        <div className="container-fluid p-0">
          <ChildChatConversation
            chat={currentChat}
            currentUser={currentUser}
            onBack={handleBackToList}
            onStartCall={handleStartCall}
          />
        </div>

        {/* Mobile Bottom Navbar */}
        <ChildMobileNavbar
          activeSection={activeSection}
          onSectionChange={handleSectionChange}
          unreadCount={unreadCount}
        />

        {/* Call Interface */}
        <CallInterface
          call={activeCall}
          isActive={isCallActive}
          onEndCall={handleEndCall}
          onToggleVideo={handleToggleVideo}
          onToggleMute={handleToggleMute}
        />
      </div>
    );
  }

  // Default chat list view
  return (
    <div className="child-chat-page">
      {/* Header */}
      <div className="child-chat-header text-center p-3">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-auto">
              <button
                className="btn btn-outline-secondary btn-sm mobile-touch-target"
                onClick={() => navigate('/child-dashboard')}
                title="Voltar"
                style={{
                  background: 'linear-gradient(135deg, #fff 0%, #fff8f0 100%)',
                  border: '2px solid var(--st-orange)',
                  borderRadius: '15px',
                  minWidth: '44px',
                  minHeight: '44px'
                }}
              >
                <i className="fas fa-arrow-left" style={{ color: 'var(--st-orange)' }}></i>
                <span className="d-none d-sm-inline ms-1" style={{ color: 'var(--st-orange)' }}>Voltar</span>
              </button>
            </div>
            <div className="col">
              <div className="child-chat-avatar mb-2">💬</div>
              <h2 className="child-chat-title mb-1">Chat da Família</h2>
              <p className="child-chat-subtitle mb-0">
                🏠 Conversa com a tua família
              </p>
            </div>
            <div className="col-auto">
              <div className="btn-group">
                <button 
                  className="btn btn-outline-primary btn-sm"
                  onClick={() => handleStartCall(CallType.AUDIO)}
                  title="Chamada de voz"
                >
                  <i className="fas fa-phone"></i>
                </button>
                <button 
                  className="btn btn-outline-primary btn-sm"
                  onClick={() => handleStartCall(CallType.VIDEO)}
                  title="Chamada de vídeo"
                >
                  <i className="fas fa-video"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Pills */}
      <ChatFilterPills
        activeFilter={activeFilter}
        onFilterChange={setActiveFilter}
        unreadCount={unreadCount}
        archivedCount={archivedCount}
      />

      {/* Chat List */}
      <div className="chat-main">
        <div className="child-chat-list">
          <div
            className="chat-list"
            onScroll={handleScroll}
          >
          {filteredChats.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-comments fa-3x text-muted mb-3"></i>
              <h5 className="text-muted">Nenhuma conversa encontrada</h5>
              <p className="text-muted">
                {activeFilter === ChatFilterType.UNREAD && 'Não tens mensagens não lidas'}
                {activeFilter === ChatFilterType.ARCHIVED && 'Não tens conversas arquivadas'}
                {activeFilter === ChatFilterType.RECEIVED && 'Não tens mensagens recebidas'}
                {activeFilter === ChatFilterType.ALL && 'Começa uma conversa!'}
              </p>
            </div>
          ) : (
            <>
              {filteredChats.map(chat => (
                <ChildChatItem
                  key={chat.id}
                  chat={chat}
                  currentUser={currentUser}
                  onChatSelect={handleChatSelect}
                  onSwipeAction={handleSwipeAction}
                />
              ))}
              
              {loading && (
                <div className="text-center py-3">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">A carregar...</span>
                  </div>
                </div>
              )}
            </>
          )}
          </div>
        </div>
      </div>

      {/* Mobile Bottom Navbar */}
      <ChildMobileNavbar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        unreadCount={unreadCount}
      />

      {/* Call Interface */}
      <CallInterface
        call={activeCall}
        isActive={isCallActive}
        onEndCall={handleEndCall}
        onToggleVideo={handleToggleVideo}
        onToggleMute={handleToggleMute}
      />
    </div>
  );
};

export default ChildChatPage;