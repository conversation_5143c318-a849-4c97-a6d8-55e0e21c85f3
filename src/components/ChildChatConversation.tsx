import React, { useState, useEffect, useRef } from 'react';
import ChildMessageInput from './ChildMessageInput';
import { Chat, ChatMessage, FamilyMember } from '../types/schema';
import { CallType, MessageType, MessageStatus, ChatType } from '../types/enums';
import { mockChatMessages } from '../data/ChatMockData';

interface ChildChatConversationProps {
  chat: Chat;
  currentUser: FamilyMember;
  onBack: () => void;
  onStartCall: (type: CallType) => void;
}

/**
 * ChildChatConversation component - Simplified conversation view for children
 * Uses ChildMessageInput with minimal design and audio recording
 */
const ChildChatConversation: React.FC<ChildChatConversationProps> = ({
  chat,
  currentUser,
  onBack,
  onStartCall
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>(mockChatMessages);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getOtherParticipant = (): FamilyMember | null => {
    if (chat.type === ChatType.PRIVATE) {
      return chat.participants.find(p => p.id !== currentUser.id) || null;
    }
    return null;
  };

  const getChatTitle = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return chat.name;
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.name || chat.name;
  };

  const handleSendMessage = (content: string) => {
    if (!content.trim()) return;

    const newMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: content.trim(),
      type: MessageType.TEXT,
      timestamp: new Date(),
      status: MessageStatus.SENT
    };

    setMessages(prev => [...prev, newMessage]);
    
    // Simulate response after a delay
    setTimeout(() => {
      const responseMessage: ChatMessage = {
        id: `msg-${Date.now() + 1}`,
        senderId: chat.type === ChatType.FAMILY ? 'parent-1' : getOtherParticipant()?.id || 'parent-1',
        senderName: chat.type === ChatType.FAMILY ? 'Mãe' : getOtherParticipant()?.name || 'Mãe',
        content: 'Obrigado pela mensagem! 😊',
        type: MessageType.TEXT,
        timestamp: new Date(),
        status: MessageStatus.SENT
      };
      setMessages(prev => [...prev, responseMessage]);
    }, 1000);
  };

  const handleSendAudio = () => {
    console.log('Recording audio message...');
    
    // Simulate audio message
    setTimeout(() => {
      const audioMessage: ChatMessage = {
        id: `msg-${Date.now()}`,
        senderId: currentUser.id,
        senderName: currentUser.name,
        content: '🎤 Mensagem de áudio',
        type: MessageType.AUDIO,
        timestamp: new Date(),
        status: MessageStatus.SENT
      };
      setMessages(prev => [...prev, audioMessage]);
    }, 1000);
  };

  const getOnlineStatus = (): string => {
    if (chat.type === ChatType.FAMILY) {
      return 'Família online';
    }
    const otherParticipant = getOtherParticipant();
    return otherParticipant?.onlineStatus === 'online' ? 'Online' : 'Offline';
  };

  return (
    <div className="child-chat-conversation d-flex flex-column" style={{ height: 'calc(100vh - 200px)', marginTop: '20px' }}>
      {/* Messages Area */}
      <div
        className="messages-container flex-grow-1 overflow-auto p-3"
        style={{
          minHeight: '0',
          paddingBottom: '80px' // Space for fixed input
        }}
      >
        {isLoading ? (
          <div className="text-center py-4">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">A carregar...</span>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isOwn = message.senderId === currentUser.id;
              const showAvatar = index === 0 ||
                messages[index - 1].senderId !== message.senderId;

              return (
                <div
                  key={message.id}
                  className={`mb-3 d-flex ${isOwn ? 'justify-content-end' : 'justify-content-start'} align-items-end`}
                >
                  {!isOwn && showAvatar && (
                    <div className="child-message-avatar me-2">
                      {message.senderName.charAt(0)}
                    </div>
                  )}

                  <div
                    className={`message-wrapper ${isOwn ? 'text-end' : 'text-start'}`}
                    style={{ maxWidth: isOwn ? '85%' : '85%' }}
                  >
                    <div className={`child-message-bubble ${isOwn ? 'sent' : 'received'}`}>
                      {!isOwn && chat.type === ChatType.FAMILY && (
                        <div className="child-message-sender">
                          {message.senderName}
                        </div>
                      )}

                      <div className="child-message-content">
                        {message.content}
                      </div>

                      <div className="child-message-time text-center">
                        {message.timestamp.toLocaleTimeString('pt-PT', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                        {isOwn && (
                          <span className="ms-2">
                            <i className="fas fa-check" style={{ opacity: 0.7 }}></i>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {isOwn && showAvatar && (
                    <div className="child-message-avatar ms-2">
                      {currentUser.name.charAt(0)}
                    </div>
                  )}
                </div>
              );
            })}
          </>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input - Fixed above mobile navbar */}
      <div 
        className="message-input-fixed p-3 position-fixed"
        style={{
          backgroundColor: '#ffffff',
          borderTop: '1px solid #dee2e6',
          bottom: '80px', // Above mobile navbar
          left: 0,
          right: 0,
          zIndex: 999
        }}
      >
        <ChildMessageInput
          onSendMessage={handleSendMessage}
          onSendAudio={handleSendAudio}
        />
      </div>
    </div>
  );
};

export default ChildChatConversation;
