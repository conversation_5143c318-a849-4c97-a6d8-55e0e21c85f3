import React, { useState, useRef } from 'react';
import EmojiPicker from './EmojiPicker';

interface ChildMessageInputProps {
  onSendMessage: (content: string) => void;
  onSendAudio?: () => void;
  disabled?: boolean;
}

/**
 * ChildMessageInput component - Simplified message input for children
 * Features minimal design with audio recording capability
 */
const ChildMessageInput: React.FC<ChildMessageInputProps> = ({ 
  onSendMessage, 
  onSendAudio,
  disabled = false
}) => {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAudioOptions, setShowAudioOptions] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const audioButtonRef = useRef<HTMLButtonElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    const textarea = textareaRef.current;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newMessage = message.slice(0, start) + emoji + message.slice(end);
      setMessage(newMessage);
      
      // Focus back to textarea and set cursor position
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + emoji.length, start + emoji.length);
      }, 0);
    }
    setShowEmojiPicker(false);
  };

  const handleAudioMouseDown = () => {
    if (onSendAudio && !disabled) {
      setIsRecording(true);
      setShowAudioOptions(false);
    }
  };

  const handleAudioMouseUp = () => {
    if (isRecording) {
      setIsRecording(false);
      setShowAudioOptions(true);
    }
  };

  const handleAudioMouseLeave = () => {
    if (isRecording) {
      setIsRecording(false);
      setShowAudioOptions(true);
    }
  };

  const handleSendAudio = () => {
    if (onSendAudio) {
      onSendAudio();
    }
    setShowAudioOptions(false);
  };

  const handleDeleteAudio = () => {
    setShowAudioOptions(false);
  };

  return (
    <div className="child-message-input-container">
      <form onSubmit={handleSubmit} className="d-flex align-items-end gap-2">
        <div className="flex-grow-1 position-relative">
          <textarea
            ref={textareaRef}
            className="form-control child-message-input"
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="escrever mensagem..."
            disabled={disabled}
            rows={1}
            style={{
              fontSize: window.innerWidth <= 768 ? '16px' : '14px',
              minHeight: '44px',
              maxHeight: '120px',
              resize: 'none',
              border: '2px solid #dee2e6',
              borderRadius: '25px',
              padding: '12px 16px',
              paddingRight: '50px'
            }}
          />
          
          {/* Emoji button inside textarea */}
          <button
            type="button"
            className="btn btn-link position-absolute mobile-touch-target"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            disabled={disabled}
            title="Emoji"
            style={{
              top: '50%',
              right: '8px',
              transform: 'translateY(-50%)',
              width: '32px',
              height: '32px',
              padding: '0',
              fontSize: '14px',
              color: '#6c757d',
              zIndex: 10
            }}
          >
            <i className="fas fa-smile"></i>
          </button>
        </div>
        
        <div className="position-relative">
          <button
            ref={audioButtonRef}
            type="button"
            className={`btn ${isRecording ? 'btn-danger' : 'btn-outline-secondary'} rounded-circle mobile-touch-target`}
            onMouseDown={handleAudioMouseDown}
            onMouseUp={handleAudioMouseUp}
            onMouseLeave={handleAudioMouseLeave}
            onTouchStart={handleAudioMouseDown}
            onTouchEnd={handleAudioMouseUp}
            disabled={disabled}
            title={isRecording ? "A gravar..." : "Manter pressionado para gravar"}
            style={{ width: '48px', height: '48px' }}
          >
            <i className={`fas ${isRecording ? 'fa-stop' : 'fa-microphone'}`}></i>
          </button>
          
          {/* Audio options popup */}
          {showAudioOptions && (
            <div 
              className="position-absolute bg-white border rounded shadow p-2 d-flex gap-2"
              style={{
                bottom: '45px',
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 1000,
                minWidth: '120px'
              }}
            >
              <button
                type="button"
                className="btn btn-success btn-sm"
                onClick={handleSendAudio}
                title="Enviar áudio"
              >
                <i className="fas fa-check"></i>
              </button>
              <button
                type="button"
                className="btn btn-danger btn-sm"
                onClick={handleDeleteAudio}
                title="Apagar áudio"
              >
                <i className="fas fa-trash"></i>
              </button>
            </div>
          )}
        </div>
        
        <button
          type="submit"
          className={`btn btn-primary rounded-circle ${message.trim() ? 'btn-send-active' : ''}`}
          disabled={!message.trim() || disabled}
          title="Enviar mensagem"
          style={{ width: '40px', height: '40px' }}
        >
          <i className="fas fa-paper-plane"></i>
        </button>
      </form>
      
      {/* Emoji Picker */}
      <EmojiPicker
        isVisible={showEmojiPicker}
        onEmojiSelect={handleEmojiSelect}
        onClose={() => setShowEmojiPicker(false)}
      />
    </div>
  );
};

export default ChildMessageInput;
